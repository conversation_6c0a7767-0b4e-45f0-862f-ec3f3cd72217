"use client"

import React, { useEffect, useMemo, useState } from "react"
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'

// 在浏览器端按网格切图并打包为 zip 下载
export default function ImageSplitPage() {
  const [file, setFile] = useState<File | null>(null)
  const [mode, setMode] = useState<"count" | "grid">("count")
  const [count, setCount] = useState<number>(9) // 总块数 n
  const [rows, setRows] = useState<number>(3)
  const [cols, setCols] = useState<number>(3)
  const [format, setFormat] = useState<"image/png" | "image/jpeg">("image/png")
  const [quality, setQuality] = useState<number>(0.92) // 仅 jpeg 生效
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  // 预览相关
  const [imageUrl, setImageUrl] = useState<string | null>(null)
  const [imgSize, setImgSize] = useState<{ w: number; h: number } | null>(null)
  const [previewing, setPreviewing] = useState(false)
  const [previewUrls, setPreviewUrls] = useState<string[]>([])
  const [showGrid, setShowGrid] = useState(true)

  const computedGrid = useMemo(() => {
    if (mode === "grid") return { rows, cols }
    const n = Math.max(1, Math.floor(count || 1))
    const r = Math.floor(Math.sqrt(n))
    const c = Math.ceil(n / r)
    return { rows: r, cols: c }
  }, [mode, count, rows, cols])

  // 根据文件生成对象 URL，并计算原图尺寸
  useEffect(() => {
    if (!file) {
      setImageUrl(null)
      setImgSize(null)
      setPreviewUrls([])
      return
    }
    const url = URL.createObjectURL(file)
    setImageUrl(url)
    let cancelled = false
    ;(async () => {
      try {
        const img = await loadImage(url)
        if (!cancelled) setImgSize({ w: img.width, h: img.height })
      } catch (e) {
        console.error(e)
      }
    })()
    return () => {
      cancelled = true
      URL.revokeObjectURL(url)
    }
  }, [file])

  // 实时生成切片缩略图（PNG 预览，不受导出格式限制），简单防抖
  useEffect(() => {
    if (!file) {
      setPreviewUrls([])
      return
    }
    const { rows, cols } = computedGrid
    if (rows < 1 || cols < 1) {
      setPreviewUrls([])
      return
    }
    let cancelled = false
    setPreviewing(true)
    const timer = setTimeout(async () => {
      try {
        const url = URL.createObjectURL(file)
        const img = await loadImage(url)
        const urls: string[] = []
        for (let r = 0; r < rows; r++) {
          for (let c = 0; c < cols; c++) {
            const { sx, sy, sw, sh } = computeSliceBounds(img.width, img.height, cols, rows, c, r)
            // 生成缩略图预览：较小尺寸以提升性能
            const maxSide = 180
            const scale = Math.min(1, maxSide / Math.max(sw, sh))
            const tw = Math.max(1, Math.round(sw * scale))
            const th = Math.max(1, Math.round(sh * scale))
            const canvas = document.createElement("canvas")
            canvas.width = tw
            canvas.height = th
            const ctx = canvas.getContext("2d")!
            ctx.imageSmoothingQuality = "high"
            ctx.drawImage(img, sx, sy, sw, sh, 0, 0, tw, th)
            const dataUrl = canvas.toDataURL("image/png")
            urls.push(dataUrl)
          }
        }
        URL.revokeObjectURL(url)
        if (!cancelled) setPreviewUrls(urls)
      } catch (e) {
        console.error(e)
        if (!cancelled) setPreviewUrls([])
      } finally {
        if (!cancelled) setPreviewing(false)
      }
    }, 200)
    return () => {
      cancelled = true
      clearTimeout(timer)
    }
  }, [file, computedGrid])

  // 叠加网格线（以百分比定位，避免依赖实际像素）
  const gridLines = useMemo(() => {
    const hs = Array.from({ length: Math.max(0, computedGrid.rows - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.rows)
    const vs = Array.from({ length: Math.max(0, computedGrid.cols - 1) }, (_, i) => ((i + 1) * 100) / computedGrid.cols)
    return { hs, vs }
  }, [computedGrid])

  async function splitAndZip() {
    try {
      setError(null)
      if (!file) {
        setError("请先选择一张图片")
        return
      }

      const { rows, cols } = computedGrid
      if (rows < 1 || cols < 1) {
        setError("行列数需大于等于 1")
        return
      }

      setLoading(true)

      const imgUrl = URL.createObjectURL(file)
      const img = await loadImage(imgUrl)

      // 动态导入 jszip（需安装依赖 jszip）
      const JSZip = (await import("jszip")).default
      const zip = new JSZip()

      for (let r = 0; r < rows; r++) {
        for (let c = 0; c < cols; c++) {
          const { sx, sy, sw, sh } = computeSliceBounds(img.width, img.height, cols, rows, c, r)
          const blob = await sliceToBlob(img, sx, sy, sw, sh, format, quality)
          const idx = r * cols + c
          zip.file(`slice_${pad(idx + 1, 3)}_r${r + 1}_c${c + 1}${format === "image/png" ? ".png" : ".jpg"}`, blob)
        }
      }

      const zipBlob = await zip.generateAsync({ type: "blob" })
      const a = document.createElement("a")
      a.href = URL.createObjectURL(zipBlob)
      a.download = `${file.name.replace(/\.[^.]+$/, "")}_slices_${rows}x${cols}.zip`
      document.body.appendChild(a)
      a.click()
      a.remove()
      // 释放对象 URL，避免内存泄漏
      URL.revokeObjectURL(a.href)
      URL.revokeObjectURL(imgUrl)
    } catch (e: any) {
      console.error(e)
      setError(e?.message || "处理失败，请稍后重试")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="mx-auto md:max-w-6xl px-4 py-10">
      <h1 className="text-3xl font-semibold tracking-tight">图片拆分下载</h1>
      <p className="text-sm text-muted-foreground mt-2">在浏览器本地完成拆分与打包，无需上传，安全高效。</p>

      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 items-start">
        {/* 左：上传 + 参数 */}
        <div className="space-y-6 xl:col-span-1 xl:sticky xl:top-24 self-start">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>选择图片</CardTitle>
              <CardDescription>支持 PNG / JPEG / WEBP</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="file">文件</Label>
                <Input
                  id="file"
                  type="file"
                  accept="image/png,image/jpeg,image/webp"
                  onChange={(e) => setFile(e.target.files?.[0] || null)}
                />
                {file && (
                  <div className="flex flex-wrap gap-2 pt-1">
                    <Badge variant="secondary" className="max-w-[18rem] truncate">{file.name}</Badge>
                    <Badge variant="outline">{formatBytes(file.size)}</Badge>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>参数设置</CardTitle>
              <CardDescription>选择拆分模式、网格与导出格式</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>拆分模式</Label>
                <RadioGroup
                  className="grid grid-cols-2 gap-4 sm:w-[360px]"
                  value={mode}
                  onValueChange={(v) => setMode(v as any)}
                >
                  <div className="flex items-center gap-2">
                    <RadioGroupItem id="mode-count" value="count" />
                    <Label htmlFor="mode-count" className="cursor-pointer">按总数量</Label>
                  </div>
                  <div className="flex items-center gap-2">
                    <RadioGroupItem id="mode-grid" value="grid" />
                    <Label htmlFor="mode-grid" className="cursor-pointer">自定义 (行 × 列)</Label>
                  </div>
                </RadioGroup>
              </div>

              {mode === "count" ? (
                <div className="space-y-2">
                  <Label htmlFor="count">总块数 n</Label>
                  <Input
                    id="count"
                    className="w-40"
                    type="number"
                    min={1}
                    value={count}
                    onChange={(e) => setCount(parseInt(e.target.value || "1", 10))}
                  />
                  <p className="text-xs text-muted-foreground">将自动计算接近方形的行列数。例如 9 → 3×3。</p>
                </div>
              ) : (
                <div className="flex gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="rows">行数</Label>
                    <Input
                      id="rows"
                      className="w-28"
                      type="number"
                      min={1}
                      value={rows}
                      onChange={(e) => setRows(parseInt(e.target.value || "1", 10))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cols">列数</Label>
                    <Input
                      id="cols"
                      className="w-28"
                      type="number"
                      min={1}
                      value={cols}
                      onChange={(e) => setCols(parseInt(e.target.value || "1", 10))}
                    />
                  </div>
                </div>
              )}

              <div className="flex items-center justify-between">
                <Label className="text-sm text-muted-foreground">显示网格线</Label>
                <Switch checked={showGrid} onCheckedChange={setShowGrid} />
              </div>

              <div className="flex gap-4 items-end">
                <div className="space-y-2">
                  <Label>导出格式</Label>
                  <Select value={format} onValueChange={(v) => setFormat(v as any)}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="选择格式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="image/png">PNG（无损）</SelectItem>
                      <SelectItem value="image/jpeg">JPEG（有损）</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {format === "image/jpeg" && (
                  <div className="space-y-2">
                    <Label htmlFor="quality">JPEG 质量（0-1）</Label>
                    <Input
                      id="quality"
                      className="w-40"
                      type="number"
                      min={0}
                      max={1}
                      step={0.01}
                      value={quality}
                      onChange={(e) => setQuality(Number(e.target.value))}
                    />
                  </div>
                )}
              </div>

              {error && (
                <div className="rounded-md border border-destructive/30 bg-destructive/10 text-destructive text-sm px-3 py-2">{error}</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 中：原图预览 */}
        <div className="xl:col-span-1 xl:sticky xl:top-24 self-start">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>原图网格预览</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative bg-muted border rounded-md overflow-hidden">
                {imageUrl && (
                  <img src={imageUrl} alt="original" className="w-full h-auto block" />
                )}
                {showGrid && (
                <div className="pointer-events-none absolute inset-0">
                  {/* 横线 */}
                  {gridLines.hs.map((p, i) => (
                    <div
                      key={`h-${i}`}
                      className="absolute left-0 right-0 bg-primary/30"
                      style={{ top: `${p}%`, height: 1 }}
                    />
                  ))}
                  {/* 竖线 */}
                  {gridLines.vs.map((p, i) => (
                    <div
                      key={`v-${i}`}
                      className="absolute top-0 bottom-0 bg-primary/30"
                      style={{ left: `${p}%`, width: 1 }}
                    />
                  ))}
                </div>
                )}
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                原图尺寸：{imgSize?.w ?? "-"} × {imgSize?.h ?? "-"}，网格：{computedGrid.rows} × {computedGrid.cols}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 右：缩略图 + 动作 */}
        <div className="xl:col-span-1 space-y-4 xl:sticky xl:top-24 self-start">
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle>切片缩略图</CardTitle>
              {!previewing && previewUrls.length > 0 && (
                <CardDescription>{previewUrls.length} 张 · {computedGrid.rows} × {computedGrid.cols}</CardDescription>
              )}
            </CardHeader>
            <CardContent>
              {previewing && <div className="text-xs text-muted-foreground">生成预览中...</div>}
              {!previewing && previewUrls.length === 0 && (
                <div className="text-xs text-muted-foreground">暂无预览</div>
              )}
              {!previewing && previewUrls.length > 0 && (
                <div className="overflow-auto max-h-[60vh] md:max-h-[70vh] overscroll-contain rounded-md border bg-muted/60 p-2">
                  <div
                    className="grid gap-2 sm:gap-3"
                    style={{ gridTemplateColumns: `repeat(${computedGrid.cols}, minmax(120px, 1fr))` }}
                  >
                    {previewUrls.map((u, i) => (
                      <div key={i} className="group relative rounded-md border bg-background overflow-hidden hover:shadow-sm transition-shadow">
                        <img src={u} alt={`slice-${i + 1}`} className="w-full h-28 md:h-32 object-contain bg-muted" />
                        <div className="absolute left-1 top-1 text-[10px] leading-none bg-foreground/60 text-background px-1.5 py-0.5 rounded">#{String(i + 1).padStart(2, "0")}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <div className="flex items-center gap-3">
            <Button onClick={splitAndZip} disabled={loading}>
              {loading ? "处理中..." : "生成并下载 ZIP"}
            </Button>
            <p className="text-xs text-muted-foreground">当前将输出：{computedGrid.rows} × {computedGrid.cols} = {computedGrid.rows * computedGrid.cols} 张</p>
          </div>
        </div>
      </div>
    </div>
  )
}

function pad(n: number, len: number) {
  return String(n).padStart(len, "0")
}

function loadImage(url: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = "anonymous"
    img.onload = () => resolve(img)
    img.onerror = reject
    img.src = url
  })
}

function computeSliceBounds(
  imgW: number,
  imgH: number,
  cols: number,
  rows: number,
  colIndex: number,
  rowIndex: number
) {
  // 使用整除分配余数，保证每块刚好覆盖整张图且无缝
  const sx = Math.floor((colIndex * imgW) / cols)
  const ex = Math.floor(((colIndex + 1) * imgW) / cols)
  const sy = Math.floor((rowIndex * imgH) / rows)
  const ey = Math.floor(((rowIndex + 1) * imgH) / rows)
  return { sx, sy, sw: ex - sx, sh: ey - sy }
}

async function sliceToBlob(
  img: HTMLImageElement,
  sx: number,
  sy: number,
  sw: number,
  sh: number,
  type: "image/png" | "image/jpeg",
  quality: number
): Promise<Blob> {
  const canvas = document.createElement("canvas")
  canvas.width = sw
  canvas.height = sh
  const ctx = canvas.getContext("2d")!
  ctx.drawImage(img, sx, sy, sw, sh, 0, 0, sw, sh)
  const blob = await new Promise<Blob>((resolve, reject) => {
    canvas.toBlob((b) => (b ? resolve(b) : reject(new Error("导出失败"))), type, quality)
  })
  return blob
}

function formatBytes(bytes: number) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${(bytes / Math.pow(k, i)).toFixed(i === 0 ? 0 : 2)} ${sizes[i]}`
}
